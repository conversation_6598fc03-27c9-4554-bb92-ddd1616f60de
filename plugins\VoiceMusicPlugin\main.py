import tomllib
import traceback
import os
import time
from typing import Optional
from pathlib import Path
from collections import defaultdict

import httpx
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class VoiceMusicPlugin(PluginBase):
    """语音点歌插件"""
    
    description = "语音点歌 - 搜索并播放音乐"
    author = "Claude"
    version = "1.0.0"
    plugin_name = "VoiceMusicPlugin"

    def __init__(self):
        super().__init__()

        # 初始化临时目录
        self._temp_dir = Path("temp/voice_music")
        self._temp_dir.mkdir(parents=True, exist_ok=True)

        # 频率限制相关
        self.request_records = defaultdict(list)
        self.warned_users = set()
        self.request_limit = config.get("request-limit", 3)  # 5分钟内最多允许3次点歌
        self.request_window = config.get("request-window", 300)  # 5分钟的时间窗口
        
        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件不存在，将使用默认配置")
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {} 
        
        # 基本配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["语音点歌"])
        self.command_format = config.get("command-format", "语法：语音点歌 歌名")
        
        # API配置
        self.api_url = config.get("api-url", "https://www.yx520.ltd/API/kgls/api.php")
        self.max_results = config.get("max-results", 1)  # 默认返回1首歌
        
        # 超时配置
        self.request_timeout = config.get("request-timeout", 30)
        
        logger.info(f"[{self.plugin_name}] 插件初始化完成")

    def _check_frequency(self, user_wxid: str) -> bool:
        """检查用户点歌频率"""
        current_time = time.time()
        current_records = self.request_records[user_wxid]

        # 清理过期记录
        current_records = [t for t in current_records if current_time - t <= self.request_window]
        self.request_records[user_wxid] = current_records

        # 检查是否超过限制
        if len(current_records) >= self.request_limit:
            return False

        # 添加当前请求记录
        current_records.append(current_time)

        # 如果是第一次请求，移除警告状态
        if len(current_records) == 1:
            self.warned_users.discard(user_wxid)

        return True

    async def _search_music(self, song_name: str) -> Optional[dict]:
        """搜索音乐"""
        try:
            params = {
                "msg": song_name,
                "n": self.max_results
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.api_url, 
                    params=params, 
                    timeout=self.request_timeout
                )
                
                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] API请求失败: 状态码={response.status_code}")
                    return None
                
                data = response.json()
                logger.debug(f"[{self.plugin_name}] API响应: {data}")
                
                if data.get("code") == 1000 and data.get("data"):
                    return data["data"]
                else:
                    logger.warning(f"[{self.plugin_name}] API返回错误: {data}")
                    return None
                    
        except httpx.TimeoutException:
            logger.error(f"[{self.plugin_name}] 请求超时")
            return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 搜索音乐失败: {e}")
            return None

    async def _download_music(self, music_url: str) -> Optional[bytes]:
        """下载音乐文件"""
        try:
            # 设置请求头，模拟浏览器访问
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'audio/*,*/*;q=0.9',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://www.kugou.com/'
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    music_url,
                    headers=headers,
                    timeout=self.request_timeout,
                    follow_redirects=True
                )

                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 下载音乐失败: 状态码={response.status_code}")
                    return None

                # 检查内容类型
                content_type = response.headers.get("content-type", "")
                logger.debug(f"[{self.plugin_name}] 音频文件类型: {content_type}")

                music_data = response.content
                if len(music_data) < 1024:  # 小于1KB可能是错误响应
                    logger.warning(f"[{self.plugin_name}] 下载的音频文件太小: {len(music_data)} bytes")
                    return None

                # 检查文件大小限制（微信语音消息有大小限制）
                max_size = 10 * 1024 * 1024  # 10MB限制
                if len(music_data) > max_size:
                    logger.warning(f"[{self.plugin_name}] 音频文件过大: {len(music_data)} bytes > {max_size} bytes")
                    return None

                logger.info(f"[{self.plugin_name}] 成功下载音频文件: {len(music_data)} bytes")
                return music_data

        except httpx.TimeoutException:
            logger.error(f"[{self.plugin_name}] 下载音乐超时")
            return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载音乐失败: {e}")
            return None

    async def _process_music_request(self, bot: WechatAPIClient, wxid: str, sender_wxid: str, song_name: str) -> None:
        """处理点歌请求"""
        try:
            # 检查频率限制
            if not self._check_frequency(sender_wxid):
                if sender_wxid not in self.warned_users:
                    self.warned_users.add(sender_wxid)
                    await bot.send_text_message(
                        wxid,
                        f"⚠️ 点歌太频繁啦~请稍后再试"
                    )
                return

            # 搜索音乐
            music_data = await self._search_music(song_name)
            if not music_data:
                return  # 没找到就不回复

            song_title = music_data.get("songname", "未知歌曲")
            singer = music_data.get("singer", "未知歌手")
            music_url = music_data.get("url", "")

            if not music_url:
                return  # 没有播放链接就不回复

            # 下载音乐
            music_content = await self._download_music(music_url)
            if not music_content:
                return  # 下载失败就不回复

            # 直接发送语音消息，不发送任何文字提示
            try:
                result = await bot.send_voice_message(wxid, music_content, 'mp3')
                if result and len(result) == 3 and result[2] != 0:
                    logger.info(f"[{self.plugin_name}] 成功发送音乐: {song_title} - {singer}")
                else:
                    logger.error(f"[{self.plugin_name}] 语音发送失败: {result}")
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 发送语音消息失败: {e}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理点歌请求失败: {e}")
            logger.error(f"[{self.plugin_name}] 错误详情: {traceback.format_exc()}")

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        wxid = message["FromWxid"]
        sender_wxid = message["SenderWxid"]
        content = message["Content"].strip()

        # 忽略机器人自己发送的消息
        if sender_wxid == bot.wxid:
            logger.debug(f"[{self.plugin_name}] 忽略机器人自己发送的消息")
            return

        # 检查是否是点歌命令
        command_matched = False
        song_name = ""
        
        for cmd in self.command:
            if content.startswith(cmd):
                command_matched = True
                # 提取歌名
                song_name = content[len(cmd):].strip()
                break
        
        if not command_matched:
            return
        
        # 检查歌名是否为空
        if not song_name:
            return  # 没有歌名就不回复
        
        # 处理点歌请求
        await self._process_music_request(bot, wxid, sender_wxid, song_name)

    async def on_disable(self):
        """插件禁用时清理资源"""
        await super().on_disable()
        logger.info(f"[{self.plugin_name}] 插件已禁用")
